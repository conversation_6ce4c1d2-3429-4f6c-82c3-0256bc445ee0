[{"D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\index.tsx": "1", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\reportWebVitals.ts": "2", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\App.tsx": "3", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\index.ts": "4", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\index.ts": "5", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\HomePage.tsx": "6", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractsListPage.tsx": "7", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractDetailsPage.tsx": "8", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CreateContractPage.tsx": "9", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\NotFoundPage.tsx": "10", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Navbar.tsx": "11", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Footer.tsx": "12", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Layout.tsx": "13", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\dateUtils.ts": "14", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\contract\\contractService.ts": "15", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\index.ts": "16", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\index.ts": "17", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\index.ts": "18", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\PageHeader.tsx": "19", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\LoadingSpinner.tsx": "20", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\SuccessAlert.tsx": "21", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ErrorAlert.tsx": "22", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ConfirmDialog.tsx": "23", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\api\\apiClient.ts": "24", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobCategory.ts": "25", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\WorkShift.ts": "26", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerContract.ts": "27", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\Customer.ts": "28", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobDetail.ts": "29", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\JobDetailForm.tsx": "30", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\CustomerContractForm.tsx": "31", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractDetails.tsx": "32", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkShiftForm.tsx": "33", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\workingDaysUtils.ts": "34", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\job\\jobCategoryService.ts": "35", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\customer\\customerService.ts": "36", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\currencyUtils.ts": "37", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\index.ts": "38", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerForm.tsx": "39", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerDialog.tsx": "40", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerPayment.ts": "41", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerRevenue.ts": "42", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\formatters.ts": "43", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerPaymentPage.tsx": "44", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\index.ts": "45", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\index.ts": "46", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\payment\\customerPaymentService.ts": "47", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerSearchForm.tsx": "48", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\PaymentForm.tsx": "49", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerContractList.tsx": "50", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerList.tsx": "51", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\SuccessNotification.tsx": "52", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerStatisticsPage.tsx": "53", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\statistics\\customerStatisticsService.ts": "54", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\index.ts": "55", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerRevenueList.tsx": "56", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerInvoiceList.tsx": "57", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\TimeBasedRevenue.ts": "58", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedStatisticsSelector.tsx": "59", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedRevenueDisplay.tsx": "60", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\DatePickerField.tsx": "61", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\BarChartDisplay.tsx": "62", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\StatisticsSummary.tsx": "63", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\contractCalculationUtils.ts": "64", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractAmountCalculation.tsx": "65", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkingDatesPreview.tsx": "66", "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractWorkSchedule.tsx": "67"}, {"size": 872, "mtime": 1748405565604, "results": "68", "hashOfConfig": "69"}, {"size": 425, "mtime": 1747901853368, "results": "70", "hashOfConfig": "69"}, {"size": 1793, "mtime": 1747923851728, "results": "71", "hashOfConfig": "69"}, {"size": 468, "mtime": 1747923851728, "results": "72", "hashOfConfig": "69"}, {"size": 138, "mtime": 1747902225279, "results": "73", "hashOfConfig": "69"}, {"size": 3142, "mtime": 1747910578132, "results": "74", "hashOfConfig": "69"}, {"size": 10811, "mtime": 1748423099538, "results": "75", "hashOfConfig": "69"}, {"size": 3693, "mtime": 1748423144492, "results": "76", "hashOfConfig": "69"}, {"size": 7985, "mtime": 1748423563428, "results": "77", "hashOfConfig": "69"}, {"size": 1078, "mtime": 1747902509716, "results": "78", "hashOfConfig": "69"}, {"size": 1836, "mtime": 1747910540518, "results": "79", "hashOfConfig": "69"}, {"size": 571, "mtime": 1747904248056, "results": "80", "hashOfConfig": "69"}, {"size": 605, "mtime": 1747902220275, "results": "81", "hashOfConfig": "69"}, {"size": 4372, "mtime": 1747932197380, "results": "82", "hashOfConfig": "69"}, {"size": 5008, "mtime": 1748423797029, "results": "83", "hashOfConfig": "69"}, {"size": 352, "mtime": 1747975523634, "results": "84", "hashOfConfig": "69"}, {"size": 488, "mtime": 1748243825884, "results": "85", "hashOfConfig": "69"}, {"size": 259, "mtime": 1747927809976, "results": "86", "hashOfConfig": "69"}, {"size": 577, "mtime": 1747902174150, "results": "87", "hashOfConfig": "69"}, {"size": 992, "mtime": 1747902149527, "results": "88", "hashOfConfig": "69"}, {"size": 1691, "mtime": 1748423840781, "results": "89", "hashOfConfig": "69"}, {"size": 2240, "mtime": 1747926050567, "results": "90", "hashOfConfig": "69"}, {"size": 1316, "mtime": 1747906323357, "results": "91", "hashOfConfig": "69"}, {"size": 11987, "mtime": 1748423749285, "results": "92", "hashOfConfig": "69"}, {"size": 155, "mtime": 1747901999771, "results": "93", "hashOfConfig": "69"}, {"size": 292, "mtime": 1747998489731, "results": "94", "hashOfConfig": "69"}, {"size": 624, "mtime": 1747976181168, "results": "95", "hashOfConfig": "69"}, {"size": 252, "mtime": 1747922140447, "results": "96", "hashOfConfig": "69"}, {"size": 346, "mtime": 1747902012555, "results": "97", "hashOfConfig": "69"}, {"size": 10047, "mtime": 1748404442915, "results": "98", "hashOfConfig": "69"}, {"size": 14304, "mtime": 1748423784905, "results": "99", "hashOfConfig": "69"}, {"size": 18568, "mtime": 1748445761160, "results": "100", "hashOfConfig": "69"}, {"size": 8747, "mtime": 1748404489650, "results": "101", "hashOfConfig": "69"}, {"size": 3543, "mtime": 1747998520066, "results": "102", "hashOfConfig": "69"}, {"size": 948, "mtime": 1747902060729, "results": "103", "hashOfConfig": "69"}, {"size": 1363, "mtime": 1747907859946, "results": "104", "hashOfConfig": "69"}, {"size": 790, "mtime": 1747906005248, "results": "105", "hashOfConfig": "69"}, {"size": 120, "mtime": 1747907779789, "results": "106", "hashOfConfig": "69"}, {"size": 5622, "mtime": 1748368160684, "results": "107", "hashOfConfig": "69"}, {"size": 12344, "mtime": 1747987012484, "results": "108", "hashOfConfig": "69"}, {"size": 415, "mtime": 1747930255221, "results": "109", "hashOfConfig": "69"}, {"size": 4238, "mtime": 1747931208285, "results": "110", "hashOfConfig": "69"}, {"size": 1351, "mtime": 1747932215756, "results": "111", "hashOfConfig": "69"}, {"size": 11928, "mtime": 1748423590766, "results": "112", "hashOfConfig": "69"}, {"size": 213, "mtime": 1747922856213, "results": "113", "hashOfConfig": "69"}, {"size": 330, "mtime": 1747911990280, "results": "114", "hashOfConfig": "69"}, {"size": 2979, "mtime": 1748423808717, "results": "115", "hashOfConfig": "69"}, {"size": 3892, "mtime": 1747910324864, "results": "116", "hashOfConfig": "69"}, {"size": 10874, "mtime": 1748423770679, "results": "117", "hashOfConfig": "69"}, {"size": 6737, "mtime": 1747976502128, "results": "118", "hashOfConfig": "69"}, {"size": 6283, "mtime": 1747911777952, "results": "119", "hashOfConfig": "69"}, {"size": 2257, "mtime": 1747912064565, "results": "120", "hashOfConfig": "69"}, {"size": 28838, "mtime": 1747994625869, "results": "121", "hashOfConfig": "69"}, {"size": 24610, "mtime": 1748340655090, "results": "122", "hashOfConfig": "69"}, {"size": 368, "mtime": 1747928054242, "results": "123", "hashOfConfig": "69"}, {"size": 2838, "mtime": 1747921471664, "results": "124", "hashOfConfig": "69"}, {"size": 3188, "mtime": 1747932337817, "results": "125", "hashOfConfig": "69"}, {"size": 1849, "mtime": 1747994411130, "results": "126", "hashOfConfig": "69"}, {"size": 6202, "mtime": 1747995320916, "results": "127", "hashOfConfig": "69"}, {"size": 7312, "mtime": 1747994456699, "results": "128", "hashOfConfig": "69"}, {"size": 2066, "mtime": 1747975512424, "results": "129", "hashOfConfig": "69"}, {"size": 3267, "mtime": 1747978131060, "results": "130", "hashOfConfig": "69"}, {"size": 2607, "mtime": 1747994496465, "results": "131", "hashOfConfig": "69"}, {"size": 5887, "mtime": 1748242274081, "results": "132", "hashOfConfig": "69"}, {"size": 7640, "mtime": 1748232092851, "results": "133", "hashOfConfig": "69"}, {"size": 6044, "mtime": 1748449376491, "results": "134", "hashOfConfig": "69"}, {"size": 7009, "mtime": 1748449027232, "results": "135", "hashOfConfig": "69"}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10nwx99", {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\index.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\reportWebVitals.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\App.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\HomePage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractsListPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\ContractDetailsPage.tsx", ["337", "338", "339"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CreateContractPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\NotFoundPage.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Navbar.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Footer.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\layout\\Layout.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\dateUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\contract\\contractService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\PageHeader.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\LoadingSpinner.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\SuccessAlert.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ErrorAlert.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\ConfirmDialog.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\api\\apiClient.ts", ["340"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobCategory.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\WorkShift.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerContract.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\Customer.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\JobDetail.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\JobDetailForm.tsx", ["341", "342", "343", "344"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\CustomerContractForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractDetails.tsx", ["345"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkShiftForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\workingDaysUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\job\\jobCategoryService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\customer\\customerService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\currencyUtils.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\customer\\CustomerDialog.tsx", ["346", "347", "348", "349", "350", "351", "352", "353", "354"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerPayment.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\CustomerRevenue.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\formatters.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerPaymentPage.tsx", ["355"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\payment\\customerPaymentService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerSearchForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\PaymentForm.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerContractList.tsx", ["356"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\CustomerList.tsx", ["357", "358", "359"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\payment\\SuccessNotification.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\pages\\CustomerStatisticsPage.tsx", [], ["360"], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\services\\statistics\\customerStatisticsService.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\index.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerRevenueList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\CustomerInvoiceList.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\models\\TimeBasedRevenue.ts", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedStatisticsSelector.tsx", ["361", "362", "363", "364"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\TimeBasedRevenueDisplay.tsx", ["365", "366", "367", "368"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\common\\DatePickerField.tsx", ["369"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\BarChartDisplay.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\statistics\\StatisticsSummary.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\utils\\contractCalculationUtils.ts", ["370", "371"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractAmountCalculation.tsx", ["372"], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\WorkingDatesPreview.tsx", [], [], "D:\\HeThongCongTyQuanLyNhanCong\\Microservice_With_Kubernetes\\microservice_fe\\src\\components\\contract\\ContractWorkSchedule.tsx", [], [], {"ruleId": "373", "severity": 1, "message": "374", "line": 3, "column": 23, "nodeType": "375", "messageId": "376", "endLine": 3, "endColumn": 33}, {"ruleId": "377", "severity": 1, "message": "378", "line": 34, "column": 6, "nodeType": "379", "endLine": 34, "endColumn": 10, "suggestions": "380"}, {"ruleId": "377", "severity": 1, "message": "378", "line": 56, "column": 6, "nodeType": "379", "endLine": 56, "endColumn": 10, "suggestions": "381"}, {"ruleId": "373", "severity": 1, "message": "382", "line": 18, "column": 7, "nodeType": "375", "messageId": "376", "endLine": 18, "endColumn": 22}, {"ruleId": "373", "severity": 1, "message": "383", "line": 16, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 16, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "384", "line": 17, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 17, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "385", "line": 30, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 30, "endColumn": 28}, {"ruleId": "373", "severity": 1, "message": "386", "line": 30, "column": 30, "nodeType": "375", "messageId": "376", "endLine": 30, "endColumn": 49}, {"ruleId": "373", "severity": 1, "message": "387", "line": 53, "column": 7, "nodeType": "375", "messageId": "376", "endLine": 56, "endColumn": 3}, {"ruleId": "373", "severity": 1, "message": "388", "line": 17, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 17, "endColumn": 13}, {"ruleId": "373", "severity": 1, "message": "384", "line": 21, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 21, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "389", "line": 22, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 22, "endColumn": 10}, {"ruleId": "373", "severity": 1, "message": "390", "line": 23, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 23, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "391", "line": 24, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 24, "endColumn": 14}, {"ruleId": "373", "severity": 1, "message": "392", "line": 29, "column": 8, "nodeType": "375", "messageId": "376", "endLine": 29, "endColumn": 18}, {"ruleId": "373", "severity": 1, "message": "393", "line": 30, "column": 8, "nodeType": "375", "messageId": "376", "endLine": 30, "endColumn": 20}, {"ruleId": "373", "severity": 1, "message": "386", "line": 38, "column": 21, "nodeType": "375", "messageId": "376", "endLine": 38, "endColumn": 40}, {"ruleId": "373", "severity": 1, "message": "394", "line": 53, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 53, "endColumn": 26}, {"ruleId": "373", "severity": 1, "message": "395", "line": 15, "column": 8, "nodeType": "375", "messageId": "376", "endLine": 15, "endColumn": 18}, {"ruleId": "373", "severity": 1, "message": "383", "line": 17, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 17, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "388", "line": 7, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 7, "endColumn": 13}, {"ruleId": "373", "severity": 1, "message": "389", "line": 22, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 22, "endColumn": 10}, {"ruleId": "373", "severity": 1, "message": "392", "line": 25, "column": 8, "nodeType": "375", "messageId": "376", "endLine": 25, "endColumn": 18}, {"ruleId": "377", "severity": 1, "message": "396", "line": 592, "column": 6, "nodeType": "379", "endLine": 592, "endColumn": 8, "suggestions": "397", "suppressions": "398"}, {"ruleId": "373", "severity": 1, "message": "399", "line": 1, "column": 17, "nodeType": "375", "messageId": "376", "endLine": 1, "endColumn": 25}, {"ruleId": "373", "severity": 1, "message": "400", "line": 9, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 9, "endColumn": 9}, {"ruleId": "373", "severity": 1, "message": "401", "line": 15, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 15, "endColumn": 12}, {"ruleId": "373", "severity": 1, "message": "402", "line": 17, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 17, "endColumn": 30}, {"ruleId": "373", "severity": 1, "message": "390", "line": 12, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 12, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "391", "line": 13, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 13, "endColumn": 14}, {"ruleId": "373", "severity": 1, "message": "383", "line": 14, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 14, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "389", "line": 15, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 15, "endColumn": 10}, {"ruleId": "373", "severity": 1, "message": "403", "line": 3, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 3, "endColumn": 19}, {"ruleId": "373", "severity": 1, "message": "404", "line": 1, "column": 28, "nodeType": "375", "messageId": "376", "endLine": 1, "endColumn": 37}, {"ruleId": "373", "severity": 1, "message": "405", "line": 1, "column": 39, "nodeType": "375", "messageId": "376", "endLine": 1, "endColumn": 48}, {"ruleId": "373", "severity": 1, "message": "406", "line": 25, "column": 35, "nodeType": "375", "messageId": "376", "endLine": 25, "endColumn": 63}, "@typescript-eslint/no-unused-vars", "'Typography' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchContract'. Either include it or remove the dependency array.", "ArrayExpression", ["407"], ["408"], "'checkApiGateway' is assigned a value but never used.", "'Grid' is defined but never used.", "'Chip' is defined but never used.", "'formatDateForInput' is defined but never used.", "'formatDateLocalized' is defined but never used.", "'WorkScheduleDetails' is assigned a value but never used.", "'IconButton' is defined but never used.", "'Divider' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'PersonIcon' is defined but never used.", "'BusinessIcon' is defined but never used.", "'selectedCustomer' is assigned a value but never used.", "'SearchIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCustomerStatistics'. Either include it or remove the dependency array.", ["409"], ["410"], "'useState' is defined but never used.", "'Button' is defined but never used.", "'FormLabel' is defined but never used.", "'formatDateForDisplay' is defined but never used.", "'TextField' is defined but never used.", "'JobDetail' is defined but never used.", "'WorkShift' is defined but never used.", "'ContractCalculationBreakdown' is defined but never used.", {"desc": "411", "fix": "412"}, {"desc": "411", "fix": "413"}, {"desc": "414", "fix": "415"}, {"kind": "416", "justification": "417"}, "Update the dependencies array to be: [fetchContract, id]", {"range": "418", "text": "419"}, {"range": "420", "text": "419"}, "Update the dependencies array to be: [loadCustomerStatistics]", {"range": "421", "text": "422"}, "directive", "", [1258, 1262], "[fetchContract, id]", [2038, 2042], [22117, 22119], "[loadCustomerStatistics]"]