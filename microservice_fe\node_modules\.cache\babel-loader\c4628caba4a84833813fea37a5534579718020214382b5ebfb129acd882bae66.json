{"ast": null, "code": "import _objectSpread from\"D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useNavigate}from'react-router-dom';import{Box}from'@mui/material';import{CustomerContractForm}from'../components/contract';import{contractService}from'../services/contract/contractService';import{LoadingSpinner,ErrorAlert,SuccessAlert}from'../components/common';import{calculateContractAmount}from'../utils/contractCalculationUtils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CreateContractPage=()=>{const navigate=useNavigate();const[contract,setContract]=useState({customerId:0,startingDate:'',endingDate:'',totalAmount:0,address:'',description:'',jobDetails:[],status:0// Pending\n});const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[success,setSuccess]=useState(null);const handleContractChange=updatedContract=>{setContract(updatedContract);};const validateContract=()=>{if(!contract.customerId||contract.customerId===0){setError('Vui lòng chọn khách hàng');return false;}// Contract dates are auto-calculated from job details, no need to validate manually\nif(!contract.address){setError('Vui lòng nhập địa chỉ hợp đồng');return false;}// Total amount is auto-calculated, no need to validate manually\nif(!contract.jobDetails||contract.jobDetails.length===0){setError('Vui lòng thêm ít nhất một chi tiết công việc');return false;}// Validate each job detail\nfor(const jobDetail of contract.jobDetails){if(!jobDetail.jobCategoryId||jobDetail.jobCategoryId===0){setError('Vui lòng chọn loại công việc cho tất cả chi tiết công việc');return false;}if(!jobDetail.startDate){setError('Vui lòng nhập ngày bắt đầu cho tất cả chi tiết công việc');return false;}if(!jobDetail.endDate){setError('Vui lòng nhập ngày kết thúc cho tất cả chi tiết công việc');return false;}if(!jobDetail.workLocation){setError('Vui lòng nhập địa điểm làm việc cho tất cả chi tiết công việc');return false;}if(!jobDetail.workShifts||jobDetail.workShifts.length===0){setError('Vui lòng thêm ít nhất một ca làm việc cho mỗi chi tiết công việc');return false;}// Validate each work shift\nfor(const workShift of jobDetail.workShifts){if(!workShift.startTime){setError('Vui lòng nhập giờ bắt đầu cho tất cả ca làm việc');return false;}if(!workShift.endTime){setError('Vui lòng nhập giờ kết thúc cho tất cả ca làm việc');return false;}if(!workShift.numberOfWorkers||workShift.numberOfWorkers<=0){setError('Vui lòng nhập số lượng người lao động hợp lệ cho tất cả ca làm việc');return false;}if(workShift.salary===undefined||workShift.salary<0){setError('Vui lòng nhập mức lương hợp lệ cho tất cả ca làm việc');return false;}if(!workShift.workingDays){setError('Vui lòng chọn ngày làm việc cho tất cả ca làm việc');return false;}}}return true;};const handleSubmit=async()=>{// Prevent double submission with multiple checks\nif(loading){console.log('Contract submission blocked: already loading');return;}// Enhanced duplicate prevention\nconst now=Date.now();const lastSubmission=localStorage.getItem('lastContractSubmission');const submissionKey=\"contract_\".concat(contract.customerId,\"_\").concat(contract.startingDate,\"_\").concat(contract.endingDate,\"_\").concat(Math.round(contract.totalAmount||0));const lastSubmissionKey=localStorage.getItem('lastContractSubmissionKey');// Prevent rapid successive submissions\nif(lastSubmission&&now-parseInt(lastSubmission)<2000){console.log('Contract submission blocked: too rapid (within 2 seconds)');setError('Vui lòng đợi ít nhất 2 giây trước khi gửi lại');return;}// Prevent duplicate contract submissions\nif(lastSubmissionKey===submissionKey&&lastSubmission&&now-parseInt(lastSubmission)<60000){console.log('Contract submission blocked: duplicate contract detected');setError('Hợp đồng tương tự đã được gửi gần đây. Vui lòng kiểm tra lại.');return;}setError(null);if(!validateContract()){return;}// Ensure total amount is calculated before submitting\nconst calculation=calculateContractAmount(contract);const contractToSubmit=_objectSpread(_objectSpread({},contract),{},{totalAmount:calculation.totalAmount});// Mark submission time and key to prevent rapid resubmission and duplicates\nlocalStorage.setItem('lastContractSubmission',now.toString());localStorage.setItem('lastContractSubmissionKey',submissionKey);setLoading(true);try{var _createdContract$tota;console.log('🚀 Submitting contract creation request...',contractToSubmit);// Clear any previous error state\nsetError(null);const createdContract=await contractService.createContract(contractToSubmit);console.log('✅ Contract created successfully:',{id:createdContract.id,totalAmount:createdContract.totalAmount,customerId:createdContract.customerId});// Verify the contract was actually created with valid data\nif(!createdContract||!createdContract.id){throw new Error('Hợp đồng được tạo nhưng không nhận được thông tin hợp lệ từ máy chủ');}setSuccess(\"H\\u1EE3p \\u0111\\u1ED3ng #\".concat(createdContract.id,\" \\u0111\\xE3 \\u0111\\u01B0\\u1EE3c t\\u1EA1o th\\xE0nh c\\xF4ng v\\u1EDBi t\\u1ED5ng gi\\xE1 tr\\u1ECB \").concat((_createdContract$tota=createdContract.totalAmount)===null||_createdContract$tota===void 0?void 0:_createdContract$tota.toLocaleString('vi-VN'),\" VN\\u0110!\"));// Clear the submission timestamp and key on success\nlocalStorage.removeItem('lastContractSubmission');localStorage.removeItem('lastContractSubmissionKey');// Set flag to trigger refresh in contracts list\nlocalStorage.setItem('contractsListNeedsRefresh','true');// Redirect to the contracts list page after a short delay\nsetTimeout(()=>{navigate('/contracts');},2000);}catch(err){var _err$response,_err$response2;console.error('❌ Contract creation failed:',err);// Provide more specific error messages\nlet errorMessage='Đã xảy ra lỗi khi tạo hợp đồng';if(((_err$response=err.response)===null||_err$response===void 0?void 0:_err$response.status)===400){errorMessage='Dữ liệu hợp đồng không hợp lệ. Vui lòng kiểm tra lại thông tin.';}else if(((_err$response2=err.response)===null||_err$response2===void 0?void 0:_err$response2.status)===500){errorMessage='Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';}else if(err.message){errorMessage=err.message;}setError(errorMessage);// Clear the submission timestamp on error to allow retry\nlocalStorage.removeItem('lastContractSubmission');}finally{setLoading(false);}};if(loading){return/*#__PURE__*/_jsx(LoadingSpinner,{fullScreen:true});}return/*#__PURE__*/_jsxs(Box,{children:[error&&/*#__PURE__*/_jsx(ErrorAlert,{message:error}),success&&/*#__PURE__*/_jsx(SuccessAlert,{message:success}),/*#__PURE__*/_jsx(CustomerContractForm,{contract:contract,onChange:handleContractChange,onSubmit:handleSubmit,loading:loading})]});};export default CreateContractPage;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Box", "CustomerContractForm", "contractService", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "calculateContractAmount", "jsx", "_jsx", "jsxs", "_jsxs", "CreateContractPage", "navigate", "contract", "setContract", "customerId", "startingDate", "endingDate", "totalAmount", "address", "description", "jobDetails", "status", "loading", "setLoading", "error", "setError", "success", "setSuccess", "handleContractChange", "updatedContract", "validateContract", "length", "jobDetail", "jobCategoryId", "startDate", "endDate", "workLocation", "workShifts", "workShift", "startTime", "endTime", "numberOfWorkers", "salary", "undefined", "workingDays", "handleSubmit", "console", "log", "now", "Date", "lastSubmission", "localStorage", "getItem", "<PERSON><PERSON><PERSON>", "concat", "Math", "round", "lastSubmissionKey", "parseInt", "calculation", "contractToSubmit", "_objectSpread", "setItem", "toString", "_createdContract$tota", "createdContract", "createContract", "id", "Error", "toLocaleString", "removeItem", "setTimeout", "err", "_err$response", "_err$response2", "errorMessage", "response", "message", "fullScreen", "children", "onChange", "onSubmit"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/pages/CreateContractPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Box } from '@mui/material';\nimport { CustomerContractForm } from '../components/contract';\nimport { CustomerContract } from '../models';\nimport { contractService } from '../services/contract/contractService';\nimport { LoadingSpinner, ErrorAlert, SuccessAlert } from '../components/common';\nimport { calculateContractAmount } from '../utils/contractCalculationUtils';\n\nconst CreateContractPage: React.FC = () => {\n  const navigate = useNavigate();\n  const [contract, setContract] = useState<Partial<CustomerContract>>({\n    customerId: 0,\n    startingDate: '',\n    endingDate: '',\n    totalAmount: 0,\n    address: '',\n    description: '',\n    jobDetails: [],\n    status: 0 // Pending\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  const handleContractChange = (updatedContract: Partial<CustomerContract>) => {\n    setContract(updatedContract);\n  };\n\n  const validateContract = (): boolean => {\n    if (!contract.customerId || contract.customerId === 0) {\n      setError('Vui lòng chọn khách hàng');\n      return false;\n    }\n\n    // Contract dates are auto-calculated from job details, no need to validate manually\n\n    if (!contract.address) {\n      setError('Vui lòng nhập địa chỉ hợp đồng');\n      return false;\n    }\n\n    // Total amount is auto-calculated, no need to validate manually\n\n    if (!contract.jobDetails || contract.jobDetails.length === 0) {\n      setError('Vui lòng thêm ít nhất một chi tiết công việc');\n      return false;\n    }\n\n    // Validate each job detail\n    for (const jobDetail of contract.jobDetails) {\n      if (!jobDetail.jobCategoryId || jobDetail.jobCategoryId === 0) {\n        setError('Vui lòng chọn loại công việc cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.startDate) {\n        setError('Vui lòng nhập ngày bắt đầu cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.endDate) {\n        setError('Vui lòng nhập ngày kết thúc cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.workLocation) {\n        setError('Vui lòng nhập địa điểm làm việc cho tất cả chi tiết công việc');\n        return false;\n      }\n\n      if (!jobDetail.workShifts || jobDetail.workShifts.length === 0) {\n        setError('Vui lòng thêm ít nhất một ca làm việc cho mỗi chi tiết công việc');\n        return false;\n      }\n\n      // Validate each work shift\n      for (const workShift of jobDetail.workShifts) {\n        if (!workShift.startTime) {\n          setError('Vui lòng nhập giờ bắt đầu cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (!workShift.endTime) {\n          setError('Vui lòng nhập giờ kết thúc cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (!workShift.numberOfWorkers || workShift.numberOfWorkers <= 0) {\n          setError('Vui lòng nhập số lượng người lao động hợp lệ cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (workShift.salary === undefined || workShift.salary < 0) {\n          setError('Vui lòng nhập mức lương hợp lệ cho tất cả ca làm việc');\n          return false;\n        }\n\n        if (!workShift.workingDays) {\n          setError('Vui lòng chọn ngày làm việc cho tất cả ca làm việc');\n          return false;\n        }\n      }\n    }\n\n    return true;\n  };\n\n  const handleSubmit = async () => {\n    // Prevent double submission with multiple checks\n    if (loading) {\n      console.log('Contract submission blocked: already loading');\n      return;\n    }\n\n    // Enhanced duplicate prevention\n    const now = Date.now();\n    const lastSubmission = localStorage.getItem('lastContractSubmission');\n    const submissionKey = `contract_${contract.customerId}_${contract.startingDate}_${contract.endingDate}_${Math.round(contract.totalAmount || 0)}`;\n    const lastSubmissionKey = localStorage.getItem('lastContractSubmissionKey');\n\n    // Prevent rapid successive submissions\n    if (lastSubmission && (now - parseInt(lastSubmission)) < 2000) {\n      console.log('Contract submission blocked: too rapid (within 2 seconds)');\n      setError('Vui lòng đợi ít nhất 2 giây trước khi gửi lại');\n      return;\n    }\n\n    // Prevent duplicate contract submissions\n    if (lastSubmissionKey === submissionKey && lastSubmission && (now - parseInt(lastSubmission)) < 60000) {\n      console.log('Contract submission blocked: duplicate contract detected');\n      setError('Hợp đồng tương tự đã được gửi gần đây. Vui lòng kiểm tra lại.');\n      return;\n    }\n\n    setError(null);\n\n    if (!validateContract()) {\n      return;\n    }\n\n    // Ensure total amount is calculated before submitting\n    const calculation = calculateContractAmount(contract);\n    const contractToSubmit = {\n      ...contract,\n      totalAmount: calculation.totalAmount\n    };\n\n    // Mark submission time and key to prevent rapid resubmission and duplicates\n    localStorage.setItem('lastContractSubmission', now.toString());\n    localStorage.setItem('lastContractSubmissionKey', submissionKey);\n    setLoading(true);\n\n    try {\n      console.log('🚀 Submitting contract creation request...', contractToSubmit);\n\n      // Clear any previous error state\n      setError(null);\n\n      const createdContract = await contractService.createContract(contractToSubmit as CustomerContract);\n      console.log('✅ Contract created successfully:', {\n        id: createdContract.id,\n        totalAmount: createdContract.totalAmount,\n        customerId: createdContract.customerId\n      });\n\n      // Verify the contract was actually created with valid data\n      if (!createdContract || !createdContract.id) {\n        throw new Error('Hợp đồng được tạo nhưng không nhận được thông tin hợp lệ từ máy chủ');\n      }\n\n      setSuccess(`Hợp đồng #${createdContract.id} đã được tạo thành công với tổng giá trị ${createdContract.totalAmount?.toLocaleString('vi-VN')} VNĐ!`);\n\n      // Clear the submission timestamp and key on success\n      localStorage.removeItem('lastContractSubmission');\n      localStorage.removeItem('lastContractSubmissionKey');\n\n      // Set flag to trigger refresh in contracts list\n      localStorage.setItem('contractsListNeedsRefresh', 'true');\n\n      // Redirect to the contracts list page after a short delay\n      setTimeout(() => {\n        navigate('/contracts');\n      }, 2000);\n    } catch (err: any) {\n      console.error('❌ Contract creation failed:', err);\n\n      // Provide more specific error messages\n      let errorMessage = 'Đã xảy ra lỗi khi tạo hợp đồng';\n\n      if (err.response?.status === 400) {\n        errorMessage = 'Dữ liệu hợp đồng không hợp lệ. Vui lòng kiểm tra lại thông tin.';\n      } else if (err.response?.status === 500) {\n        errorMessage = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';\n      } else if (err.message) {\n        errorMessage = err.message;\n      }\n\n      setError(errorMessage);\n\n      // Clear the submission timestamp on error to allow retry\n      localStorage.removeItem('lastContractSubmission');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return <LoadingSpinner fullScreen />;\n  }\n\n  return (\n    <Box>\n      {error && <ErrorAlert message={error} />}\n      {success && <SuccessAlert message={success} />}\n\n      <CustomerContractForm\n        contract={contract}\n        onChange={handleContractChange}\n        onSubmit={handleSubmit}\n        loading={loading}\n      />\n    </Box>\n  );\n};\n\nexport default CreateContractPage;\n"], "mappings": "gKAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,GAAG,KAAQ,eAAe,CACnC,OAASC,oBAAoB,KAAQ,wBAAwB,CAE7D,OAASC,eAAe,KAAQ,sCAAsC,CACtE,OAASC,cAAc,CAAEC,UAAU,CAAEC,YAAY,KAAQ,sBAAsB,CAC/E,OAASC,uBAAuB,KAAQ,mCAAmC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5E,KAAM,CAAAC,kBAA4B,CAAGA,CAAA,GAAM,CACzC,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACc,QAAQ,CAAEC,WAAW,CAAC,CAAGhB,QAAQ,CAA4B,CAClEiB,UAAU,CAAE,CAAC,CACbC,YAAY,CAAE,EAAE,CAChBC,UAAU,CAAE,EAAE,CACdC,WAAW,CAAE,CAAC,CACdC,OAAO,CAAE,EAAE,CACXC,WAAW,CAAE,EAAE,CACfC,UAAU,CAAE,EAAE,CACdC,MAAM,CAAE,CAAE;AACZ,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC2B,KAAK,CAAEC,QAAQ,CAAC,CAAG5B,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAAC6B,OAAO,CAAEC,UAAU,CAAC,CAAG9B,QAAQ,CAAgB,IAAI,CAAC,CAE3D,KAAM,CAAA+B,oBAAoB,CAAIC,eAA0C,EAAK,CAC3EhB,WAAW,CAACgB,eAAe,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAe,CACtC,GAAI,CAAClB,QAAQ,CAACE,UAAU,EAAIF,QAAQ,CAACE,UAAU,GAAK,CAAC,CAAE,CACrDW,QAAQ,CAAC,0BAA0B,CAAC,CACpC,MAAO,MAAK,CACd,CAEA;AAEA,GAAI,CAACb,QAAQ,CAACM,OAAO,CAAE,CACrBO,QAAQ,CAAC,gCAAgC,CAAC,CAC1C,MAAO,MAAK,CACd,CAEA;AAEA,GAAI,CAACb,QAAQ,CAACQ,UAAU,EAAIR,QAAQ,CAACQ,UAAU,CAACW,MAAM,GAAK,CAAC,CAAE,CAC5DN,QAAQ,CAAC,8CAA8C,CAAC,CACxD,MAAO,MAAK,CACd,CAEA;AACA,IAAK,KAAM,CAAAO,SAAS,GAAI,CAAApB,QAAQ,CAACQ,UAAU,CAAE,CAC3C,GAAI,CAACY,SAAS,CAACC,aAAa,EAAID,SAAS,CAACC,aAAa,GAAK,CAAC,CAAE,CAC7DR,QAAQ,CAAC,4DAA4D,CAAC,CACtE,MAAO,MAAK,CACd,CAEA,GAAI,CAACO,SAAS,CAACE,SAAS,CAAE,CACxBT,QAAQ,CAAC,0DAA0D,CAAC,CACpE,MAAO,MAAK,CACd,CAEA,GAAI,CAACO,SAAS,CAACG,OAAO,CAAE,CACtBV,QAAQ,CAAC,2DAA2D,CAAC,CACrE,MAAO,MAAK,CACd,CAEA,GAAI,CAACO,SAAS,CAACI,YAAY,CAAE,CAC3BX,QAAQ,CAAC,+DAA+D,CAAC,CACzE,MAAO,MAAK,CACd,CAEA,GAAI,CAACO,SAAS,CAACK,UAAU,EAAIL,SAAS,CAACK,UAAU,CAACN,MAAM,GAAK,CAAC,CAAE,CAC9DN,QAAQ,CAAC,kEAAkE,CAAC,CAC5E,MAAO,MAAK,CACd,CAEA;AACA,IAAK,KAAM,CAAAa,SAAS,GAAI,CAAAN,SAAS,CAACK,UAAU,CAAE,CAC5C,GAAI,CAACC,SAAS,CAACC,SAAS,CAAE,CACxBd,QAAQ,CAAC,kDAAkD,CAAC,CAC5D,MAAO,MAAK,CACd,CAEA,GAAI,CAACa,SAAS,CAACE,OAAO,CAAE,CACtBf,QAAQ,CAAC,mDAAmD,CAAC,CAC7D,MAAO,MAAK,CACd,CAEA,GAAI,CAACa,SAAS,CAACG,eAAe,EAAIH,SAAS,CAACG,eAAe,EAAI,CAAC,CAAE,CAChEhB,QAAQ,CAAC,qEAAqE,CAAC,CAC/E,MAAO,MAAK,CACd,CAEA,GAAIa,SAAS,CAACI,MAAM,GAAKC,SAAS,EAAIL,SAAS,CAACI,MAAM,CAAG,CAAC,CAAE,CAC1DjB,QAAQ,CAAC,uDAAuD,CAAC,CACjE,MAAO,MAAK,CACd,CAEA,GAAI,CAACa,SAAS,CAACM,WAAW,CAAE,CAC1BnB,QAAQ,CAAC,oDAAoD,CAAC,CAC9D,MAAO,MAAK,CACd,CACF,CACF,CAEA,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAoB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B;AACA,GAAIvB,OAAO,CAAE,CACXwB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC,CAC3D,OACF,CAEA;AACA,KAAM,CAAAC,GAAG,CAAGC,IAAI,CAACD,GAAG,CAAC,CAAC,CACtB,KAAM,CAAAE,cAAc,CAAGC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,CACrE,KAAM,CAAAC,aAAa,aAAAC,MAAA,CAAe1C,QAAQ,CAACE,UAAU,MAAAwC,MAAA,CAAI1C,QAAQ,CAACG,YAAY,MAAAuC,MAAA,CAAI1C,QAAQ,CAACI,UAAU,MAAAsC,MAAA,CAAIC,IAAI,CAACC,KAAK,CAAC5C,QAAQ,CAACK,WAAW,EAAI,CAAC,CAAC,CAAE,CAChJ,KAAM,CAAAwC,iBAAiB,CAAGN,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CAE3E;AACA,GAAIF,cAAc,EAAKF,GAAG,CAAGU,QAAQ,CAACR,cAAc,CAAC,CAAI,IAAI,CAAE,CAC7DJ,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC,CACxEtB,QAAQ,CAAC,+CAA+C,CAAC,CACzD,OACF,CAEA;AACA,GAAIgC,iBAAiB,GAAKJ,aAAa,EAAIH,cAAc,EAAKF,GAAG,CAAGU,QAAQ,CAACR,cAAc,CAAC,CAAI,KAAK,CAAE,CACrGJ,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC,CACvEtB,QAAQ,CAAC,+DAA+D,CAAC,CACzE,OACF,CAEAA,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CAACK,gBAAgB,CAAC,CAAC,CAAE,CACvB,OACF,CAEA;AACA,KAAM,CAAA6B,WAAW,CAAGtD,uBAAuB,CAACO,QAAQ,CAAC,CACrD,KAAM,CAAAgD,gBAAgB,CAAAC,aAAA,CAAAA,aAAA,IACjBjD,QAAQ,MACXK,WAAW,CAAE0C,WAAW,CAAC1C,WAAW,EACrC,CAED;AACAkC,YAAY,CAACW,OAAO,CAAC,wBAAwB,CAAEd,GAAG,CAACe,QAAQ,CAAC,CAAC,CAAC,CAC9DZ,YAAY,CAACW,OAAO,CAAC,2BAA2B,CAAET,aAAa,CAAC,CAChE9B,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,KAAAyC,qBAAA,CACFlB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAEa,gBAAgB,CAAC,CAE3E;AACAnC,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAwC,eAAe,CAAG,KAAM,CAAAhE,eAAe,CAACiE,cAAc,CAACN,gBAAoC,CAAC,CAClGd,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAE,CAC9CoB,EAAE,CAAEF,eAAe,CAACE,EAAE,CACtBlD,WAAW,CAAEgD,eAAe,CAAChD,WAAW,CACxCH,UAAU,CAAEmD,eAAe,CAACnD,UAC9B,CAAC,CAAC,CAEF;AACA,GAAI,CAACmD,eAAe,EAAI,CAACA,eAAe,CAACE,EAAE,CAAE,CAC3C,KAAM,IAAI,CAAAC,KAAK,CAAC,qEAAqE,CAAC,CACxF,CAEAzC,UAAU,6BAAA2B,MAAA,CAAcW,eAAe,CAACE,EAAE,kGAAAb,MAAA,EAAAU,qBAAA,CAA4CC,eAAe,CAAChD,WAAW,UAAA+C,qBAAA,iBAA3BA,qBAAA,CAA6BK,cAAc,CAAC,OAAO,CAAC,cAAO,CAAC,CAElJ;AACAlB,YAAY,CAACmB,UAAU,CAAC,wBAAwB,CAAC,CACjDnB,YAAY,CAACmB,UAAU,CAAC,2BAA2B,CAAC,CAEpD;AACAnB,YAAY,CAACW,OAAO,CAAC,2BAA2B,CAAE,MAAM,CAAC,CAEzD;AACAS,UAAU,CAAC,IAAM,CACf5D,QAAQ,CAAC,YAAY,CAAC,CACxB,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAO6D,GAAQ,CAAE,KAAAC,aAAA,CAAAC,cAAA,CACjB5B,OAAO,CAACtB,KAAK,CAAC,6BAA6B,CAAEgD,GAAG,CAAC,CAEjD;AACA,GAAI,CAAAG,YAAY,CAAG,gCAAgC,CAEnD,GAAI,EAAAF,aAAA,CAAAD,GAAG,CAACI,QAAQ,UAAAH,aAAA,iBAAZA,aAAA,CAAcpD,MAAM,IAAK,GAAG,CAAE,CAChCsD,YAAY,CAAG,iEAAiE,CAClF,CAAC,IAAM,IAAI,EAAAD,cAAA,CAAAF,GAAG,CAACI,QAAQ,UAAAF,cAAA,iBAAZA,cAAA,CAAcrD,MAAM,IAAK,GAAG,CAAE,CACvCsD,YAAY,CAAG,2CAA2C,CAC5D,CAAC,IAAM,IAAIH,GAAG,CAACK,OAAO,CAAE,CACtBF,YAAY,CAAGH,GAAG,CAACK,OAAO,CAC5B,CAEApD,QAAQ,CAACkD,YAAY,CAAC,CAEtB;AACAxB,YAAY,CAACmB,UAAU,CAAC,wBAAwB,CAAC,CACnD,CAAC,OAAS,CACR/C,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAID,OAAO,CAAE,CACX,mBAAOf,IAAA,CAACL,cAAc,EAAC4E,UAAU,MAAE,CAAC,CACtC,CAEA,mBACErE,KAAA,CAACV,GAAG,EAAAgF,QAAA,EACDvD,KAAK,eAAIjB,IAAA,CAACJ,UAAU,EAAC0E,OAAO,CAAErD,KAAM,CAAE,CAAC,CACvCE,OAAO,eAAInB,IAAA,CAACH,YAAY,EAACyE,OAAO,CAAEnD,OAAQ,CAAE,CAAC,cAE9CnB,IAAA,CAACP,oBAAoB,EACnBY,QAAQ,CAAEA,QAAS,CACnBoE,QAAQ,CAAEpD,oBAAqB,CAC/BqD,QAAQ,CAAEpC,YAAa,CACvBvB,OAAO,CAAEA,OAAQ,CAClB,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAZ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}