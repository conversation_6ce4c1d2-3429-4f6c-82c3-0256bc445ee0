import React, { useState } from 'react';
import {
  Box,
  Typography,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Card,
  CardContent,
  Divider,
  useTheme,
  Avatar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import BusinessIcon from '@mui/icons-material/Business';
import DateRangeIcon from '@mui/icons-material/DateRange';
import DescriptionIcon from '@mui/icons-material/Description';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import WorkIcon from '@mui/icons-material/Work';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import { CustomerContract, ContractStatusMap } from '../../models';
import { formatDateLocalized } from '../../utils/dateUtils';
import { formatWorkingDays, calculateWorkingDates } from '../../utils/workingDaysUtils';
import { formatCurrency } from '../../utils/currencyUtils';
import ContractWorkSchedule from './ContractWorkSchedule';

// Mapping for Vietnamese day names
const dayNames: { [key: number]: string } = {
  1: 'Thứ Hai',
  2: 'Thứ Ba',
  3: 'Thứ Tư',
  4: 'Thứ Năm',
  5: 'Thứ Sáu',
  6: 'Thứ Bảy',
  7: 'Chủ Nhật'
};

interface ContractDetailsProps {
  contract: CustomerContract;
}

// Component to display detailed work schedule
const WorkScheduleDetails: React.FC<{
  workShift: any;
  jobDetail: any;
}> = ({ workShift, jobDetail }) => {
  const [expanded, setExpanded] = useState(false);

  // Calculate working dates for this shift
  const workingDates = calculateWorkingDates(
    jobDetail.startDate,
    jobDetail.endDate,
    workShift.workingDays
  );

  // Group dates by day of week
  const datesByDayOfWeek: { [key: number]: string[] } = {};
  workingDates.forEach(dateStr => {
    const date = new Date(dateStr);
    const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay(); // Convert Sunday from 0 to 7
    if (!datesByDayOfWeek[dayOfWeek]) {
      datesByDayOfWeek[dayOfWeek] = [];
    }
    datesByDayOfWeek[dayOfWeek].push(formatDateLocalized(dateStr));
  });

  return (
    <Accordion expanded={expanded} onChange={() => setExpanded(!expanded)}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
          <CalendarMonthIcon sx={{ mr: 1 }} />
          <Typography variant="body2">
            Lịch làm việc chi tiết ({workingDates.length} ngày)
          </Typography>
        </Box>
      </AccordionSummary>
      <AccordionDetails>
        <Box>
          <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 'bold' }}>
            Lịch làm việc theo thứ trong tuần:
          </Typography>

          {Object.entries(datesByDayOfWeek)
            .sort(([a], [b]) => parseInt(a) - parseInt(b))
            .map(([dayOfWeek, dates]) => (
              <Box key={dayOfWeek} sx={{ mb: 2 }}>
                <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 1 }}>
                  {dayNames[parseInt(dayOfWeek)]}:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {dates.map((date, index) => (
                    <Chip
                      key={index}
                      label={date}
                      size="small"
                      variant="outlined"
                      color="primary"
                    />
                  ))}
                </Box>
              </Box>
            ))}

          <Typography variant="body2" sx={{ mt: 2, fontStyle: 'italic', color: 'text.secondary' }}>
            Tổng cộng: {workingDates.length} ngày làm việc
          </Typography>
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};

const ContractDetails: React.FC<ContractDetailsProps> = ({ contract }) => {
  const theme = useTheme();

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0: // Pending
        return 'warning';
      case 1: // Active
        return 'success';
      case 2: // Completed
        return 'info';
      case 3: // Cancelled
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusBgColor = (status: number) => {
    switch (status) {
      case 0: // Pending
        return theme.palette.warning.light;
      case 1: // Active
        return theme.palette.success.light;
      case 2: // Completed
        return theme.palette.info.light;
      case 3: // Cancelled
        return theme.palette.error.light;
      default:
        return theme.palette.grey[200];
    }
  };

  return (
    <Box>
      <Card
        elevation={3}
        sx={{
          mb: 4,
          borderRadius: '8px',
          border: '1px solid #e0e0e0',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Contract header with status */}
        <Box
          sx={{
            p: 3,
            backgroundColor: getStatusBgColor(contract.status || 0),
            borderBottom: '1px solid #e0e0e0',
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Avatar
                sx={{
                  bgcolor: theme.palette.primary.main,
                  mr: 2,
                  width: 56,
                  height: 56,
                }}
              >
                <DescriptionIcon fontSize="large" />
              </Avatar>
              <Box>
                <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                  HỢP ĐỒNG #{contract.id}
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  Mã hợp đồng: {contract.id}
                </Typography>
              </Box>
            </Box>
            <Chip
              label={ContractStatusMap[contract.status || 0]}
              color={getStatusColor(contract.status || 0)}
              sx={{
                fontSize: '1rem',
                py: 2,
                px: 3,
                fontWeight: 'bold',
              }}
            />
          </Box>
        </Box>

        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
            {/* Customer information */}
            <Box sx={{ width: { xs: '100%', md: '48%' } }}>
              <Card variant="outlined" sx={{ mb: 2, height: '100%' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <PersonIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      Thông tin khách hàng
                    </Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {contract.customerName}
                  </Typography>
                </CardContent>
              </Card>
            </Box>

            {/* Location information */}
            <Box sx={{ width: { xs: '100%', md: '48%' } }}>
              <Card variant="outlined" sx={{ mb: 2, height: '100%' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <BusinessIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      Địa điểm làm việc
                    </Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="body1">
                    {contract.address}
                  </Typography>
                </CardContent>
              </Card>
            </Box>

            {/* Contract dates */}
            <Box sx={{ width: { xs: '100%', md: '48%' } }}>
              <Card variant="outlined" sx={{ mb: 2 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <DateRangeIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      Thời gian thực hiện
                    </Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                    <Box sx={{ width: { xs: '100%', sm: '30%' } }}>
                      <Typography variant="body2" color="text.secondary">
                        Ngày bắt đầu:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                        {formatDateLocalized(contract.startingDate)}
                      </Typography>
                    </Box>
                    <Box sx={{ width: { xs: '100%', sm: '30%' } }}>
                      <Typography variant="body2" color="text.secondary">
                        Ngày kết thúc:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                        {formatDateLocalized(contract.endingDate)}
                      </Typography>
                    </Box>

                  </Box>
                </CardContent>
              </Card>
            </Box>

            {/* Financial information */}
            <Box sx={{ width: { xs: '100%', md: '48%' } }}>
              <Card variant="outlined" sx={{ mb: 2 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <MonetizationOnIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      Thông tin thanh toán
                    </Typography>
                  </Box>
                  <Divider sx={{ mb: 2 }} />
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                    <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                      <Typography variant="body2" color="text.secondary">
                        Tổng giá trị hợp đồng:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                        {formatCurrency(contract.totalAmount)}
                      </Typography>
                    </Box>
                    <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                      <Typography variant="body2" color="text.secondary">
                        Đã thanh toán:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
                        {formatCurrency(contract.totalPaid || 0)}
                      </Typography>
                    </Box>
                    <Box sx={{ width: { xs: '100%', sm: '48%' } }}>
                      <Typography variant="body2" color="text.secondary">
                        Còn lại:
                      </Typography>
                      <Typography variant="body1" sx={{ fontWeight: 'bold', color: theme.palette.error.main }}>
                        {formatCurrency(contract.totalAmount - (contract.totalPaid || 0))}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Box>

            {/* Description if available */}
            {contract.description && (
              <Box sx={{ width: '100%' }}>
                <Card variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <DescriptionIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                      <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                        Mô tả hợp đồng
                      </Typography>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    <Typography variant="body1">
                      {contract.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Box>
            )}
          </Box>
        </CardContent>
      </Card>

      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <WorkIcon sx={{ mr: 1, color: theme.palette.secondary.main, fontSize: 28 }} />
          <Typography variant="h5" sx={{ fontWeight: 'bold', color: theme.palette.secondary.main }}>
            CHI TIẾT CÔNG VIỆC
          </Typography>
        </Box>

        {contract.jobDetails.map((jobDetail, index) => (
          <Card
            key={index}
            variant="outlined"
            sx={{
              mb: 3,
              borderRadius: '8px',
              border: '1px solid #e0e0e0',
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '6px',
                background: theme.palette.secondary.main,
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <WorkIcon sx={{ mr: 1, color: theme.palette.secondary.main }} />
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  {jobDetail.jobCategoryName}
                </Typography>
              </Box>

              <Divider sx={{ mb: 3 }} />

              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mb: 3 }}>
                <Box sx={{ width: { xs: '100%', md: '31%' } }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <LocationOnIcon fontSize="small" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />
                    <Typography variant="body2" color="text.secondary">
                      Địa điểm làm việc
                    </Typography>
                  </Box>
                  <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                    {jobDetail.workLocation}
                  </Typography>
                </Box>

                <Box sx={{ width: { xs: '100%', md: '31%' } }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <DateRangeIcon fontSize="small" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />
                    <Typography variant="body2" color="text.secondary">
                      Ngày bắt đầu
                    </Typography>
                  </Box>
                  <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                    {formatDateLocalized(jobDetail.startDate)}
                  </Typography>
                </Box>

                <Box sx={{ width: { xs: '100%', md: '31%' } }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <DateRangeIcon fontSize="small" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />
                    <Typography variant="body2" color="text.secondary">
                      Ngày kết thúc
                    </Typography>
                  </Box>
                  <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                    {formatDateLocalized(jobDetail.endDate)}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <AccessTimeIcon sx={{ mr: 1, color: theme.palette.info.main }} />
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: theme.palette.info.main }}>
                    Ca làm việc
                  </Typography>
                </Box>

                <TableContainer sx={{
                  border: '1px solid #e0e0e0',
                  borderRadius: '8px',
                  '& .MuiTableCell-head': {
                    backgroundColor: theme.palette.info.light,
                    fontWeight: 'bold',
                  }
                }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Giờ bắt đầu</TableCell>
                        <TableCell>Giờ kết thúc</TableCell>
                        <TableCell>Số lượng người lao động</TableCell>
                        <TableCell>Lương (VNĐ)</TableCell>
                        <TableCell>Ngày làm việc</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {jobDetail.workShifts.map((shift, shiftIndex) => (
                        <React.Fragment key={shiftIndex}>
                          <TableRow hover>
                            <TableCell>{shift.startTime}</TableCell>
                            <TableCell>{shift.endTime}</TableCell>
                            <TableCell>{shift.numberOfWorkers}</TableCell>
                            <TableCell>{formatCurrency(shift.salary || 0)}</TableCell>
                            <TableCell>{formatWorkingDays(shift.workingDays)}</TableCell>
                          </TableRow>
                        </React.Fragment>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
              {/* Lịch làm việc chi tiết cho công việc này */}
              <ContractWorkSchedule contract={{...contract, jobDetails: [jobDetail]}} />
            </CardContent>
          </Card>
        ))}
      </Box>
    </Box>
  );
};

export default ContractDetails;
