import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  useTheme,
  Paper,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import WorkIcon from '@mui/icons-material/Work';
import { CustomerContract } from '../../models';
import { calculateWorkingDates, formatWorkingDays } from '../../utils/workingDaysUtils';
import { formatCurrency } from '../../utils/formatters';
import { formatDateLocalized } from '../../utils/dateUtils';

// Mapping for Vietnamese day names
const dayNames: { [key: number]: string } = {
  1: 'Thứ Hai',
  2: 'Thứ Ba',
  3: '<PERSON><PERSON><PERSON>',
  4: '<PERSON><PERSON><PERSON>',
  5: '<PERSON><PERSON><PERSON>',
  6: '<PERSON><PERSON><PERSON>',
  7: '<PERSON><PERSON>'
};

interface ContractWorkScheduleProps {
  contract: CustomerContract;
}

const ContractWorkSchedule: React.FC<ContractWorkScheduleProps> = ({ contract }) => {
  const [expandedShifts, setExpandedShifts] = useState<{ [key: string]: boolean }>({});
  const theme = useTheme();

  const handleShiftExpand = (shiftKey: string) => {
    setExpandedShifts(prev => ({
      ...prev,
      [shiftKey]: !prev[shiftKey]
    }));
  };

  return (
    <Box>
      {contract.jobDetails.length > 1 && (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <CalendarMonthIcon sx={{ mr: 1, color: theme.palette.primary.main, fontSize: 28 }} />
          <Typography variant="h5" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
            LỊCH LÀM VIỆC CHI TIẾT
          </Typography>
        </Box>
      )}
      {contract.jobDetails.map((jobDetail, jobIndex) => {
        let allShifts: { date: string; startTime: string; endTime: string }[] = [];
        jobDetail.workShifts.forEach(shift => {
          const workingDates = calculateWorkingDates(
            jobDetail.startDate,
            jobDetail.endDate,
            shift.workingDays
          );
          workingDates.forEach(date => {
            allShifts.push({ date, startTime: shift.startTime, endTime: shift.endTime });
          });
        });
        allShifts.sort((a, b) => {
          const [d1, m1, y1] = a.date.split('/').map(Number);
          const [d2, m2, y2] = b.date.split('/').map(Number);
          return new Date(y1, m1 - 1, d1).getTime() - new Date(y2, m2 - 1, d2).getTime();
        });
        const getDayOfWeek = (dateStr: string) => {
          const [d, m, y] = dateStr.split('/').map(Number);
          const date = new Date(y, m - 1, d);
          const day = date.getDay();
          return day === 0 ? 'Chủ nhật' : `Thứ ${day + 1}`;
        };
        // Nếu chỉ có 1 jobDetail thì không bọc Card ngoài, chỉ hiển thị danh sách
        if (contract.jobDetails.length === 1) {
          return (
            <Box key={jobIndex}>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>
                Lịch làm việc chi tiết:
              </Typography>
              {allShifts.length === 0 ? (
                <Typography color="text.secondary">Không có lịch làm việc</Typography>
              ) : (
                <Box component="ul" sx={{ pl: 3, mb: 0 }}>
                  {allShifts.map((item, idx) => (
                    <li key={idx} style={{ marginBottom: 4 }}>
                      <Typography variant="body2">
                        {getDayOfWeek(item.date)}, ngày {item.date} ca {item.startTime} - {item.endTime}
                      </Typography>
                    </li>
                  ))}
                </Box>
              )}
            </Box>
          );
        }
        // Nếu nhiều jobDetail thì giữ nguyên Card ngoài
        return (
          <Card
            key={jobIndex}
            elevation={3}
            sx={{
              mb: 3,
              borderRadius: '12px',
              border: '2px solid',
              borderColor: theme.palette.primary.light,
              overflow: 'hidden',
            }}
          >
            <Box
              sx={{
                p: 3,
                backgroundColor: theme.palette.primary.light,
                borderBottom: '1px solid',
                borderColor: theme.palette.primary.main,
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <WorkIcon sx={{ mr: 2, color: theme.palette.primary.main, fontSize: 32 }} />
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                      {jobDetail.jobCategoryName}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {formatDateLocalized(jobDetail.startDate)} - {formatDateLocalized(jobDetail.endDate)}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ textAlign: 'right' }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
                    {formatCurrency(
                      jobDetail.workShifts.reduce((total, shift) => {
                        const workingDates = calculateWorkingDates(
                          jobDetail.startDate,
                          jobDetail.endDate,
                          shift.workingDays
                        );
                        return total + (shift.salary || 0) * (shift.numberOfWorkers || 0) * workingDates.length;
                      }, 0)
                    )}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {allShifts.length} ngày làm việc
                  </Typography>
                </Box>
              </Box>
            </Box>
            <CardContent>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>
                Lịch làm việc chi tiết:
              </Typography>
              {allShifts.length === 0 ? (
                <Typography color="text.secondary">Không có lịch làm việc</Typography>
              ) : (
                <Box component="ul" sx={{ pl: 3, mb: 0 }}>
                  {allShifts.map((item, idx) => (
                    <li key={idx} style={{ marginBottom: 4 }}>
                      <Typography variant="body2">
                        {getDayOfWeek(item.date)}, ngày {item.date} ca {item.startTime} - {item.endTime}
                      </Typography>
                    </li>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        );
      })}
    </Box>
  );
};

export default ContractWorkSchedule;
