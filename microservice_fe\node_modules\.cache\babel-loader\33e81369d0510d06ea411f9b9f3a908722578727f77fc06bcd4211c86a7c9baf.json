{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,Alert,Snackbar,Paper,Tabs,Tab,Divider,Button,useTheme,useMediaQuery}from'@mui/material';import PersonIcon from'@mui/icons-material/Person';import PaymentIcon from'@mui/icons-material/Payment';import{PageHeader,LoadingSpinner,ErrorAlert}from'../components/common';import{CustomerList,CustomerContractList,PaymentForm,SuccessNotification}from'../components/payment';import{customerPaymentService,customerService}from'../services';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CustomerPaymentPage=()=>{const theme=useTheme();const isMobile=useMediaQuery(theme.breakpoints.down('sm'));// State for tabs\nconst[tabValue,setTabValue]=useState(0);// State for customers\nconst[customers,setCustomers]=useState([]);const[selectedCustomer,setSelectedCustomer]=useState(null);const[searchTerm,setSearchTerm]=useState('');// State for contracts\nconst[contracts,setContracts]=useState([]);const[selectedContract,setSelectedContract]=useState(null);const[remainingAmount,setRemainingAmount]=useState(0);// State for payment form\nconst[paymentFormOpen,setPaymentFormOpen]=useState(false);// State for UI\nconst[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[successMessage,setSuccessMessage]=useState(null);const[showSuccessNotification,setShowSuccessNotification]=useState(false);// Load customers on initial render\nuseEffect(()=>{fetchCustomers();},[]);// Fetch all customers\nconst fetchCustomers=async()=>{setLoading(true);try{const result=await customerService.getAllCustomers();setCustomers(result);}catch(err){console.error('Error fetching customers:',err);setError('Đã xảy ra lỗi khi tải danh sách khách hàng');}finally{setLoading(false);}};// Handle tab change\nconst handleTabChange=(_event,newValue)=>{setTabValue(newValue);};// Handle customer search\nconst handleSearch=async term=>{setSearchTerm(term);if(!term.trim())return;setLoading(true);try{// Tìm kiếm theo cả tên và số điện thoại\nconst result=await customerPaymentService.searchCustomers(term,term);setCustomers(result);if(result.length===0){setError('Không tìm thấy khách hàng nào phù hợp');}else{setError(null);}}catch(err){console.error('Error searching customers:',err);setError('Đã xảy ra lỗi khi tìm kiếm khách hàng');}finally{setLoading(false);}};// Handle customer selection\nconst handleSelectCustomer=async customer=>{setSelectedCustomer(customer);setTabValue(1);// Switch to contracts tab\nsetLoading(true);setError(null);try{const activeContracts=await customerPaymentService.getActiveContractsByCustomerId(customer.id);setContracts(activeContracts);}catch(err){console.error('Error fetching contracts:',err);setError('Đã xảy ra lỗi khi tải danh sách hợp đồng');}finally{setLoading(false);}};// Handle payment button click\nconst handlePaymentClick=async contract=>{setSelectedContract(contract);setLoading(true);try{// Get the latest contract payment info\nconst contractInfo=await customerPaymentService.getContractPaymentInfo(contract.id);const remaining=await customerPaymentService.getRemainingAmountByContractId(contract.id);setSelectedContract(contractInfo);setRemainingAmount(remaining);setPaymentFormOpen(true);}catch(err){console.error('Error fetching contract payment info:',err);setError('Đã xảy ra lỗi khi tải thông tin thanh toán hợp đồng');}finally{setLoading(false);}};// Handle payment form close\nconst handlePaymentFormClose=()=>{setPaymentFormOpen(false);};// Handle payment form submit\nconst handlePaymentSubmit=async payment=>{// Prevent double submission with multiple checks\nif(loading){console.log('Payment submission blocked: already loading');return;}// Enhanced duplicate prevention\nconst now=Date.now();const lastSubmission=localStorage.getItem('lastPaymentSubmission');const submissionKey=\"payment_\".concat(payment.customerContractId,\"_\").concat(payment.paymentAmount,\"_\").concat(payment.paymentMethod);const lastSubmissionKey=localStorage.getItem('lastPaymentSubmissionKey');// Prevent rapid successive submissions\nif(lastSubmission&&now-parseInt(lastSubmission)<2000){console.log('Payment submission blocked: too rapid (within 2 seconds)');setError('Vui lòng đợi ít nhất 2 giây trước khi gửi lại');return;}// Prevent duplicate payment submissions\nif(lastSubmissionKey===submissionKey&&lastSubmission&&now-parseInt(lastSubmission)<60000){console.log('Payment submission blocked: duplicate payment detected');setError('Thanh toán tương tự đã được gửi gần đây. Vui lòng kiểm tra lại.');return;}// Mark submission time and key to prevent rapid resubmission and duplicates\nlocalStorage.setItem('lastPaymentSubmission',now.toString());localStorage.setItem('lastPaymentSubmissionKey',submissionKey);setLoading(true);setError(null);try{var _createdPayment$payme;console.log('🚀 Submitting payment creation request...',payment);// Clear any previous error state\nsetError(null);const createdPayment=await customerPaymentService.createPayment(payment);console.log('✅ Payment created successfully:',{id:createdPayment.id,amount:createdPayment.paymentAmount,contractId:createdPayment.customerContractId});// Verify the payment was actually created with valid data\nif(!createdPayment||!createdPayment.id){throw new Error('Thanh toán được tạo nhưng không nhận được thông tin hợp lệ từ máy chủ');}setSuccessMessage(\"Thanh to\\xE1n #\".concat(createdPayment.id,\" th\\xE0nh c\\xF4ng v\\u1EDBi s\\u1ED1 ti\\u1EC1n \").concat((_createdPayment$payme=createdPayment.paymentAmount)===null||_createdPayment$payme===void 0?void 0:_createdPayment$payme.toLocaleString('vi-VN'),\" VN\\u0110!\"));setPaymentFormOpen(false);setShowSuccessNotification(true);// Clear the submission timestamp and key on success\nlocalStorage.removeItem('lastPaymentSubmission');localStorage.removeItem('lastPaymentSubmissionKey');// Refresh contracts list to show updated payment information\nif(selectedCustomer){try{const activeContracts=await customerPaymentService.getActiveContractsByCustomerId(selectedCustomer.id);setContracts(activeContracts);console.log('✅ Contracts list refreshed after payment');}catch(refreshError){console.warn('⚠️ Failed to refresh contracts list:',refreshError);// Don't show error for refresh failure, payment was successful\n}}// Set flag to trigger refresh in contracts list page\nlocalStorage.setItem('contractsListNeedsRefresh','true');}catch(err){var _err$response,_err$response2;console.error('❌ Payment creation failed:',err);// Provide more specific error messages\nlet errorMessage='Đã xảy ra lỗi khi tạo thanh toán';if(((_err$response=err.response)===null||_err$response===void 0?void 0:_err$response.status)===400){errorMessage='Dữ liệu thanh toán không hợp lệ. Vui lòng kiểm tra lại thông tin.';}else if(((_err$response2=err.response)===null||_err$response2===void 0?void 0:_err$response2.status)===500){errorMessage='Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';}else if(err.message){errorMessage=err.message;}setError(errorMessage);// Clear the submission timestamp on error to allow retry\nlocalStorage.removeItem('lastPaymentSubmission');}finally{setLoading(false);}};// Handle success message close\nconst handleSuccessClose=()=>{setSuccessMessage(null);};// Handle success notification close\nconst handleSuccessNotificationClose=()=>{setShowSuccessNotification(false);};// Handle back to customer list\nconst handleBackToCustomers=()=>{setTabValue(0);};return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(PageHeader,{title:\"Thanh to\\xE1n h\\u1EE3p \\u0111\\u1ED3ng kh\\xE1ch h\\xE0ng\",subtitle:\"Qu\\u1EA3n l\\xFD thanh to\\xE1n h\\u1EE3p \\u0111\\u1ED3ng kh\\xE1ch h\\xE0ng thu\\xEA lao \\u0111\\u1ED9ng\"}),error&&/*#__PURE__*/_jsx(ErrorAlert,{message:error}),/*#__PURE__*/_jsxs(Paper,{sx:{mb:3},children:[/*#__PURE__*/_jsxs(Tabs,{value:tabValue,onChange:handleTabChange,variant:isMobile?\"fullWidth\":\"standard\",sx:{borderBottom:1,borderColor:'divider'},children:[/*#__PURE__*/_jsx(Tab,{icon:/*#__PURE__*/_jsx(PersonIcon,{}),label:\"Danh s\\xE1ch kh\\xE1ch h\\xE0ng\",iconPosition:\"start\"}),selectedCustomer&&/*#__PURE__*/_jsx(Tab,{icon:/*#__PURE__*/_jsx(PaymentIcon,{}),label:\"H\\u1EE3p \\u0111\\u1ED3ng c\\u1EA7n thanh to\\xE1n\",iconPosition:\"start\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{p:3},children:[tabValue===0&&/*#__PURE__*/_jsx(CustomerList,{customers:customers,onSelectCustomer:handleSelectCustomer,onSearch:handleSearch,searchTerm:searchTerm,setSearchTerm:setSearchTerm,loading:loading}),tabValue===1&&selectedCustomer&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[\"Kh\\xE1ch h\\xE0ng: \",selectedCustomer.fullName,selectedCustomer.companyName&&\" (\".concat(selectedCustomer.companyName,\")\")]}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(PersonIcon,{}),onClick:handleBackToCustomers,children:\"Quay l\\u1EA1i danh s\\xE1ch\"})]}),/*#__PURE__*/_jsx(Divider,{sx:{mb:2}}),loading?/*#__PURE__*/_jsx(LoadingSpinner,{}):/*#__PURE__*/_jsx(CustomerContractList,{contracts:contracts,onPaymentClick:handlePaymentClick})]})]})]}),/*#__PURE__*/_jsx(PaymentForm,{open:paymentFormOpen,contract:selectedContract,onClose:handlePaymentFormClose,onSubmit:handlePaymentSubmit,remainingAmount:remainingAmount,loading:loading}),/*#__PURE__*/_jsx(SuccessNotification,{open:showSuccessNotification,message:\"Thanh to\\xE1n \\u0111\\xE3 \\u0111\\u01B0\\u1EE3c ghi nh\\u1EADn th\\xE0nh c\\xF4ng!\",onClose:handleSuccessNotificationClose}),/*#__PURE__*/_jsx(Snackbar,{open:!!successMessage,autoHideDuration:6000,onClose:handleSuccessClose,anchorOrigin:{vertical:'bottom',horizontal:'center'},children:/*#__PURE__*/_jsx(Alert,{onClose:handleSuccessClose,severity:\"success\",sx:{width:'100%'},children:successMessage})})]});};export default CustomerPaymentPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Snackbar", "Paper", "Tabs", "Tab", "Divider", "<PERSON><PERSON>", "useTheme", "useMediaQuery", "PersonIcon", "PaymentIcon", "<PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CustomerList", "CustomerContractList", "PaymentForm", "SuccessNotification", "customerPaymentService", "customerService", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CustomerPaymentPage", "theme", "isMobile", "breakpoints", "down", "tabValue", "setTabValue", "customers", "setCustomers", "selectedCustomer", "setSelectedCustomer", "searchTerm", "setSearchTerm", "contracts", "setContracts", "selectedContract", "setSelectedContract", "remainingAmount", "setRemainingAmount", "paymentFormOpen", "setPaymentFormOpen", "loading", "setLoading", "error", "setError", "successMessage", "setSuccessMessage", "showSuccessNotification", "setShowSuccessNotification", "fetchCustomers", "result", "getAllCustomers", "err", "console", "handleTabChange", "_event", "newValue", "handleSearch", "term", "trim", "searchCustomers", "length", "handleSelectCustomer", "customer", "activeContracts", "getActiveContractsByCustomerId", "id", "handlePaymentClick", "contract", "contractInfo", "getContractPaymentInfo", "remaining", "getRemainingAmountByContractId", "handlePaymentFormClose", "handlePaymentSubmit", "payment", "log", "now", "Date", "lastSubmission", "localStorage", "getItem", "<PERSON><PERSON><PERSON>", "concat", "customerContractId", "paymentAmount", "paymentMethod", "lastSubmissionKey", "parseInt", "setItem", "toString", "_createdPayment$payme", "createdPayment", "createPayment", "amount", "contractId", "Error", "toLocaleString", "removeItem", "refreshError", "warn", "_err$response", "_err$response2", "errorMessage", "response", "status", "message", "handleSuccessClose", "handleSuccessNotificationClose", "handleBackToCustomers", "children", "title", "subtitle", "sx", "mb", "value", "onChange", "variant", "borderBottom", "borderColor", "icon", "label", "iconPosition", "p", "onSelectCustomer", "onSearch", "display", "justifyContent", "alignItems", "fullName", "companyName", "startIcon", "onClick", "onPaymentClick", "open", "onClose", "onSubmit", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "width"], "sources": ["D:/HeThongCongTyQuanLyNhanCong/Microservice_With_Kubernetes/microservice_fe/src/pages/CustomerPaymentPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Alert,\n  Snackbar,\n  Paper,\n  Tabs,\n  Tab,\n  Divider,\n  Button,\n  useTheme,\n  useMediaQuery\n} from '@mui/material';\nimport SearchIcon from '@mui/icons-material/Search';\nimport PersonIcon from '@mui/icons-material/Person';\nimport PaymentIcon from '@mui/icons-material/Payment';\nimport { PageHeader, LoadingSpinner, ErrorAlert } from '../components/common';\nimport {\n  CustomerList,\n  CustomerContractList,\n  PaymentForm,\n  SuccessNotification\n} from '../components/payment';\nimport { customerPaymentService, customerService } from '../services';\nimport { Customer, CustomerContract, CustomerPayment } from '../models';\n\nconst CustomerPaymentPage: React.FC = () => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n\n  // State for tabs\n  const [tabValue, setTabValue] = useState<number>(0);\n\n  // State for customers\n  const [customers, setCustomers] = useState<Customer[]>([]);\n  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);\n  const [searchTerm, setSearchTerm] = useState<string>('');\n\n  // State for contracts\n  const [contracts, setContracts] = useState<CustomerContract[]>([]);\n  const [selectedContract, setSelectedContract] = useState<CustomerContract | null>(null);\n  const [remainingAmount, setRemainingAmount] = useState<number>(0);\n\n  // State for payment form\n  const [paymentFormOpen, setPaymentFormOpen] = useState<boolean>(false);\n\n  // State for UI\n  const [loading, setLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\n  const [showSuccessNotification, setShowSuccessNotification] = useState<boolean>(false);\n\n  // Load customers on initial render\n  useEffect(() => {\n    fetchCustomers();\n  }, []);\n\n  // Fetch all customers\n  const fetchCustomers = async () => {\n    setLoading(true);\n    try {\n      const result = await customerService.getAllCustomers();\n      setCustomers(result);\n    } catch (err) {\n      console.error('Error fetching customers:', err);\n      setError('Đã xảy ra lỗi khi tải danh sách khách hàng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle tab change\n  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {\n    setTabValue(newValue);\n  };\n\n  // Handle customer search\n  const handleSearch = async (term: string) => {\n    setSearchTerm(term);\n    if (!term.trim()) return;\n\n    setLoading(true);\n    try {\n      // Tìm kiếm theo cả tên và số điện thoại\n      const result = await customerPaymentService.searchCustomers(term, term);\n      setCustomers(result);\n\n      if (result.length === 0) {\n        setError('Không tìm thấy khách hàng nào phù hợp');\n      } else {\n        setError(null);\n      }\n    } catch (err) {\n      console.error('Error searching customers:', err);\n      setError('Đã xảy ra lỗi khi tìm kiếm khách hàng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle customer selection\n  const handleSelectCustomer = async (customer: Customer) => {\n    setSelectedCustomer(customer);\n    setTabValue(1); // Switch to contracts tab\n    setLoading(true);\n    setError(null);\n\n    try {\n      const activeContracts = await customerPaymentService.getActiveContractsByCustomerId(customer.id!);\n      setContracts(activeContracts);\n    } catch (err) {\n      console.error('Error fetching contracts:', err);\n      setError('Đã xảy ra lỗi khi tải danh sách hợp đồng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle payment button click\n  const handlePaymentClick = async (contract: CustomerContract) => {\n    setSelectedContract(contract);\n    setLoading(true);\n\n    try {\n      // Get the latest contract payment info\n      const contractInfo = await customerPaymentService.getContractPaymentInfo(contract.id!);\n      const remaining = await customerPaymentService.getRemainingAmountByContractId(contract.id!);\n\n      setSelectedContract(contractInfo);\n      setRemainingAmount(remaining);\n      setPaymentFormOpen(true);\n    } catch (err) {\n      console.error('Error fetching contract payment info:', err);\n      setError('Đã xảy ra lỗi khi tải thông tin thanh toán hợp đồng');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle payment form close\n  const handlePaymentFormClose = () => {\n    setPaymentFormOpen(false);\n  };\n\n  // Handle payment form submit\n  const handlePaymentSubmit = async (payment: CustomerPayment) => {\n    // Prevent double submission with multiple checks\n    if (loading) {\n      console.log('Payment submission blocked: already loading');\n      return;\n    }\n\n    // Enhanced duplicate prevention\n    const now = Date.now();\n    const lastSubmission = localStorage.getItem('lastPaymentSubmission');\n    const submissionKey = `payment_${payment.customerContractId}_${payment.paymentAmount}_${payment.paymentMethod}`;\n    const lastSubmissionKey = localStorage.getItem('lastPaymentSubmissionKey');\n\n    // Prevent rapid successive submissions\n    if (lastSubmission && (now - parseInt(lastSubmission)) < 2000) {\n      console.log('Payment submission blocked: too rapid (within 2 seconds)');\n      setError('Vui lòng đợi ít nhất 2 giây trước khi gửi lại');\n      return;\n    }\n\n    // Prevent duplicate payment submissions\n    if (lastSubmissionKey === submissionKey && lastSubmission && (now - parseInt(lastSubmission)) < 60000) {\n      console.log('Payment submission blocked: duplicate payment detected');\n      setError('Thanh toán tương tự đã được gửi gần đây. Vui lòng kiểm tra lại.');\n      return;\n    }\n\n    // Mark submission time and key to prevent rapid resubmission and duplicates\n    localStorage.setItem('lastPaymentSubmission', now.toString());\n    localStorage.setItem('lastPaymentSubmissionKey', submissionKey);\n    setLoading(true);\n    setError(null);\n\n    try {\n      console.log('🚀 Submitting payment creation request...', payment);\n\n      // Clear any previous error state\n      setError(null);\n\n      const createdPayment = await customerPaymentService.createPayment(payment);\n      console.log('✅ Payment created successfully:', {\n        id: createdPayment.id,\n        amount: createdPayment.paymentAmount,\n        contractId: createdPayment.customerContractId\n      });\n\n      // Verify the payment was actually created with valid data\n      if (!createdPayment || !createdPayment.id) {\n        throw new Error('Thanh toán được tạo nhưng không nhận được thông tin hợp lệ từ máy chủ');\n      }\n\n      setSuccessMessage(`Thanh toán #${createdPayment.id} thành công với số tiền ${createdPayment.paymentAmount?.toLocaleString('vi-VN')} VNĐ!`);\n      setPaymentFormOpen(false);\n      setShowSuccessNotification(true);\n\n      // Clear the submission timestamp and key on success\n      localStorage.removeItem('lastPaymentSubmission');\n      localStorage.removeItem('lastPaymentSubmissionKey');\n\n      // Refresh contracts list to show updated payment information\n      if (selectedCustomer) {\n        try {\n          const activeContracts = await customerPaymentService.getActiveContractsByCustomerId(selectedCustomer.id!);\n          setContracts(activeContracts);\n          console.log('✅ Contracts list refreshed after payment');\n        } catch (refreshError) {\n          console.warn('⚠️ Failed to refresh contracts list:', refreshError);\n          // Don't show error for refresh failure, payment was successful\n        }\n      }\n\n      // Set flag to trigger refresh in contracts list page\n      localStorage.setItem('contractsListNeedsRefresh', 'true');\n    } catch (err: any) {\n      console.error('❌ Payment creation failed:', err);\n\n      // Provide more specific error messages\n      let errorMessage = 'Đã xảy ra lỗi khi tạo thanh toán';\n\n      if (err.response?.status === 400) {\n        errorMessage = 'Dữ liệu thanh toán không hợp lệ. Vui lòng kiểm tra lại thông tin.';\n      } else if (err.response?.status === 500) {\n        errorMessage = 'Lỗi máy chủ nội bộ. Vui lòng thử lại sau.';\n      } else if (err.message) {\n        errorMessage = err.message;\n      }\n\n      setError(errorMessage);\n\n      // Clear the submission timestamp on error to allow retry\n      localStorage.removeItem('lastPaymentSubmission');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle success message close\n  const handleSuccessClose = () => {\n    setSuccessMessage(null);\n  };\n\n  // Handle success notification close\n  const handleSuccessNotificationClose = () => {\n    setShowSuccessNotification(false);\n  };\n\n  // Handle back to customer list\n  const handleBackToCustomers = () => {\n    setTabValue(0);\n  };\n\n  return (\n    <Box>\n      <PageHeader\n        title=\"Thanh toán hợp đồng khách hàng\"\n        subtitle=\"Quản lý thanh toán hợp đồng khách hàng thuê lao động\"\n      />\n\n      {error && <ErrorAlert message={error} />}\n\n      <Paper sx={{ mb: 3 }}>\n        <Tabs\n          value={tabValue}\n          onChange={handleTabChange}\n          variant={isMobile ? \"fullWidth\" : \"standard\"}\n          sx={{ borderBottom: 1, borderColor: 'divider' }}\n        >\n          <Tab\n            icon={<PersonIcon />}\n            label=\"Danh sách khách hàng\"\n            iconPosition=\"start\"\n          />\n          {selectedCustomer && (\n            <Tab\n              icon={<PaymentIcon />}\n              label=\"Hợp đồng cần thanh toán\"\n              iconPosition=\"start\"\n            />\n          )}\n        </Tabs>\n\n        <Box sx={{ p: 3 }}>\n          {tabValue === 0 && (\n            <CustomerList\n              customers={customers}\n              onSelectCustomer={handleSelectCustomer}\n              onSearch={handleSearch}\n              searchTerm={searchTerm}\n              setSearchTerm={setSearchTerm}\n              loading={loading}\n            />\n          )}\n\n          {tabValue === 1 && selectedCustomer && (\n            <>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n                <Typography variant=\"h6\">\n                  Khách hàng: {selectedCustomer.fullName}\n                  {selectedCustomer.companyName && ` (${selectedCustomer.companyName})`}\n                </Typography>\n                <Button\n                  variant=\"outlined\"\n                  startIcon={<PersonIcon />}\n                  onClick={handleBackToCustomers}\n                >\n                  Quay lại danh sách\n                </Button>\n              </Box>\n              <Divider sx={{ mb: 2 }} />\n\n              {loading ? (\n                <LoadingSpinner />\n              ) : (\n                <CustomerContractList\n                  contracts={contracts}\n                  onPaymentClick={handlePaymentClick}\n                />\n              )}\n            </>\n          )}\n        </Box>\n      </Paper>\n\n      <PaymentForm\n        open={paymentFormOpen}\n        contract={selectedContract}\n        onClose={handlePaymentFormClose}\n        onSubmit={handlePaymentSubmit}\n        remainingAmount={remainingAmount}\n        loading={loading}\n      />\n\n      <SuccessNotification\n        open={showSuccessNotification}\n        message=\"Thanh toán đã được ghi nhận thành công!\"\n        onClose={handleSuccessNotificationClose}\n      />\n\n      <Snackbar\n        open={!!successMessage}\n        autoHideDuration={6000}\n        onClose={handleSuccessClose}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleSuccessClose} severity=\"success\" sx={{ width: '100%' }}>\n          {successMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default CustomerPaymentPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,QAAQ,CACRC,KAAK,CACLC,IAAI,CACJC,GAAG,CACHC,OAAO,CACPC,MAAM,CACNC,QAAQ,CACRC,aAAa,KACR,eAAe,CAEtB,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,OAASC,UAAU,CAAEC,cAAc,CAAEC,UAAU,KAAQ,sBAAsB,CAC7E,OACEC,YAAY,CACZC,oBAAoB,CACpBC,WAAW,CACXC,mBAAmB,KACd,uBAAuB,CAC9B,OAASC,sBAAsB,CAAEC,eAAe,KAAQ,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAGtE,KAAM,CAAAC,mBAA6B,CAAGA,CAAA,GAAM,CAC1C,KAAM,CAAAC,KAAK,CAAGpB,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAqB,QAAQ,CAAGpB,aAAa,CAACmB,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAE5D;AACA,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGpC,QAAQ,CAAS,CAAC,CAAC,CAEnD;AACA,KAAM,CAACqC,SAAS,CAAEC,YAAY,CAAC,CAAGtC,QAAQ,CAAa,EAAE,CAAC,CAC1D,KAAM,CAACuC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxC,QAAQ,CAAkB,IAAI,CAAC,CAC/E,KAAM,CAACyC,UAAU,CAAEC,aAAa,CAAC,CAAG1C,QAAQ,CAAS,EAAE,CAAC,CAExD;AACA,KAAM,CAAC2C,SAAS,CAAEC,YAAY,CAAC,CAAG5C,QAAQ,CAAqB,EAAE,CAAC,CAClE,KAAM,CAAC6C,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG9C,QAAQ,CAA0B,IAAI,CAAC,CACvF,KAAM,CAAC+C,eAAe,CAAEC,kBAAkB,CAAC,CAAGhD,QAAQ,CAAS,CAAC,CAAC,CAEjE;AACA,KAAM,CAACiD,eAAe,CAAEC,kBAAkB,CAAC,CAAGlD,QAAQ,CAAU,KAAK,CAAC,CAEtE;AACA,KAAM,CAACmD,OAAO,CAAEC,UAAU,CAAC,CAAGpD,QAAQ,CAAU,KAAK,CAAC,CACtD,KAAM,CAACqD,KAAK,CAAEC,QAAQ,CAAC,CAAGtD,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACuD,cAAc,CAAEC,iBAAiB,CAAC,CAAGxD,QAAQ,CAAgB,IAAI,CAAC,CACzE,KAAM,CAACyD,uBAAuB,CAAEC,0BAA0B,CAAC,CAAG1D,QAAQ,CAAU,KAAK,CAAC,CAEtF;AACAC,SAAS,CAAC,IAAM,CACd0D,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAA,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjCP,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAQ,MAAM,CAAG,KAAM,CAAArC,eAAe,CAACsC,eAAe,CAAC,CAAC,CACtDvB,YAAY,CAACsB,MAAM,CAAC,CACtB,CAAE,MAAOE,GAAG,CAAE,CACZC,OAAO,CAACV,KAAK,CAAC,2BAA2B,CAAES,GAAG,CAAC,CAC/CR,QAAQ,CAAC,4CAA4C,CAAC,CACxD,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAY,eAAe,CAAGA,CAACC,MAA4B,CAAEC,QAAgB,GAAK,CAC1E9B,WAAW,CAAC8B,QAAQ,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAC,IAAY,EAAK,CAC3C1B,aAAa,CAAC0B,IAAI,CAAC,CACnB,GAAI,CAACA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAE,OAElBjB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA,KAAM,CAAAQ,MAAM,CAAG,KAAM,CAAAtC,sBAAsB,CAACgD,eAAe,CAACF,IAAI,CAAEA,IAAI,CAAC,CACvE9B,YAAY,CAACsB,MAAM,CAAC,CAEpB,GAAIA,MAAM,CAACW,MAAM,GAAK,CAAC,CAAE,CACvBjB,QAAQ,CAAC,uCAAuC,CAAC,CACnD,CAAC,IAAM,CACLA,QAAQ,CAAC,IAAI,CAAC,CAChB,CACF,CAAE,MAAOQ,GAAG,CAAE,CACZC,OAAO,CAACV,KAAK,CAAC,4BAA4B,CAAES,GAAG,CAAC,CAChDR,QAAQ,CAAC,uCAAuC,CAAC,CACnD,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAoB,oBAAoB,CAAG,KAAO,CAAAC,QAAkB,EAAK,CACzDjC,mBAAmB,CAACiC,QAAQ,CAAC,CAC7BrC,WAAW,CAAC,CAAC,CAAC,CAAE;AAChBgB,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF,KAAM,CAAAoB,eAAe,CAAG,KAAM,CAAApD,sBAAsB,CAACqD,8BAA8B,CAACF,QAAQ,CAACG,EAAG,CAAC,CACjGhC,YAAY,CAAC8B,eAAe,CAAC,CAC/B,CAAE,MAAOZ,GAAG,CAAE,CACZC,OAAO,CAACV,KAAK,CAAC,2BAA2B,CAAES,GAAG,CAAC,CAC/CR,QAAQ,CAAC,0CAA0C,CAAC,CACtD,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAyB,kBAAkB,CAAG,KAAO,CAAAC,QAA0B,EAAK,CAC/DhC,mBAAmB,CAACgC,QAAQ,CAAC,CAC7B1B,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF;AACA,KAAM,CAAA2B,YAAY,CAAG,KAAM,CAAAzD,sBAAsB,CAAC0D,sBAAsB,CAACF,QAAQ,CAACF,EAAG,CAAC,CACtF,KAAM,CAAAK,SAAS,CAAG,KAAM,CAAA3D,sBAAsB,CAAC4D,8BAA8B,CAACJ,QAAQ,CAACF,EAAG,CAAC,CAE3F9B,mBAAmB,CAACiC,YAAY,CAAC,CACjC/B,kBAAkB,CAACiC,SAAS,CAAC,CAC7B/B,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,MAAOY,GAAG,CAAE,CACZC,OAAO,CAACV,KAAK,CAAC,uCAAuC,CAAES,GAAG,CAAC,CAC3DR,QAAQ,CAAC,qDAAqD,CAAC,CACjE,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA+B,sBAAsB,CAAGA,CAAA,GAAM,CACnCjC,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAkC,mBAAmB,CAAG,KAAO,CAAAC,OAAwB,EAAK,CAC9D;AACA,GAAIlC,OAAO,CAAE,CACXY,OAAO,CAACuB,GAAG,CAAC,6CAA6C,CAAC,CAC1D,OACF,CAEA;AACA,KAAM,CAAAC,GAAG,CAAGC,IAAI,CAACD,GAAG,CAAC,CAAC,CACtB,KAAM,CAAAE,cAAc,CAAGC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,CACpE,KAAM,CAAAC,aAAa,YAAAC,MAAA,CAAcR,OAAO,CAACS,kBAAkB,MAAAD,MAAA,CAAIR,OAAO,CAACU,aAAa,MAAAF,MAAA,CAAIR,OAAO,CAACW,aAAa,CAAE,CAC/G,KAAM,CAAAC,iBAAiB,CAAGP,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAE1E;AACA,GAAIF,cAAc,EAAKF,GAAG,CAAGW,QAAQ,CAACT,cAAc,CAAC,CAAI,IAAI,CAAE,CAC7D1B,OAAO,CAACuB,GAAG,CAAC,0DAA0D,CAAC,CACvEhC,QAAQ,CAAC,+CAA+C,CAAC,CACzD,OACF,CAEA;AACA,GAAI2C,iBAAiB,GAAKL,aAAa,EAAIH,cAAc,EAAKF,GAAG,CAAGW,QAAQ,CAACT,cAAc,CAAC,CAAI,KAAK,CAAE,CACrG1B,OAAO,CAACuB,GAAG,CAAC,wDAAwD,CAAC,CACrEhC,QAAQ,CAAC,iEAAiE,CAAC,CAC3E,OACF,CAEA;AACAoC,YAAY,CAACS,OAAO,CAAC,uBAAuB,CAAEZ,GAAG,CAACa,QAAQ,CAAC,CAAC,CAAC,CAC7DV,YAAY,CAACS,OAAO,CAAC,0BAA0B,CAAEP,aAAa,CAAC,CAC/DxC,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,KAAA+C,qBAAA,CACFtC,OAAO,CAACuB,GAAG,CAAC,2CAA2C,CAAED,OAAO,CAAC,CAEjE;AACA/B,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAAgD,cAAc,CAAG,KAAM,CAAAhF,sBAAsB,CAACiF,aAAa,CAAClB,OAAO,CAAC,CAC1EtB,OAAO,CAACuB,GAAG,CAAC,iCAAiC,CAAE,CAC7CV,EAAE,CAAE0B,cAAc,CAAC1B,EAAE,CACrB4B,MAAM,CAAEF,cAAc,CAACP,aAAa,CACpCU,UAAU,CAAEH,cAAc,CAACR,kBAC7B,CAAC,CAAC,CAEF;AACA,GAAI,CAACQ,cAAc,EAAI,CAACA,cAAc,CAAC1B,EAAE,CAAE,CACzC,KAAM,IAAI,CAAA8B,KAAK,CAAC,uEAAuE,CAAC,CAC1F,CAEAlD,iBAAiB,mBAAAqC,MAAA,CAAgBS,cAAc,CAAC1B,EAAE,kDAAAiB,MAAA,EAAAQ,qBAAA,CAA2BC,cAAc,CAACP,aAAa,UAAAM,qBAAA,iBAA5BA,qBAAA,CAA8BM,cAAc,CAAC,OAAO,CAAC,cAAO,CAAC,CAC1IzD,kBAAkB,CAAC,KAAK,CAAC,CACzBQ,0BAA0B,CAAC,IAAI,CAAC,CAEhC;AACAgC,YAAY,CAACkB,UAAU,CAAC,uBAAuB,CAAC,CAChDlB,YAAY,CAACkB,UAAU,CAAC,0BAA0B,CAAC,CAEnD;AACA,GAAIrE,gBAAgB,CAAE,CACpB,GAAI,CACF,KAAM,CAAAmC,eAAe,CAAG,KAAM,CAAApD,sBAAsB,CAACqD,8BAA8B,CAACpC,gBAAgB,CAACqC,EAAG,CAAC,CACzGhC,YAAY,CAAC8B,eAAe,CAAC,CAC7BX,OAAO,CAACuB,GAAG,CAAC,0CAA0C,CAAC,CACzD,CAAE,MAAOuB,YAAY,CAAE,CACrB9C,OAAO,CAAC+C,IAAI,CAAC,sCAAsC,CAAED,YAAY,CAAC,CAClE;AACF,CACF,CAEA;AACAnB,YAAY,CAACS,OAAO,CAAC,2BAA2B,CAAE,MAAM,CAAC,CAC3D,CAAE,MAAOrC,GAAQ,CAAE,KAAAiD,aAAA,CAAAC,cAAA,CACjBjD,OAAO,CAACV,KAAK,CAAC,4BAA4B,CAAES,GAAG,CAAC,CAEhD;AACA,GAAI,CAAAmD,YAAY,CAAG,kCAAkC,CAErD,GAAI,EAAAF,aAAA,CAAAjD,GAAG,CAACoD,QAAQ,UAAAH,aAAA,iBAAZA,aAAA,CAAcI,MAAM,IAAK,GAAG,CAAE,CAChCF,YAAY,CAAG,mEAAmE,CACpF,CAAC,IAAM,IAAI,EAAAD,cAAA,CAAAlD,GAAG,CAACoD,QAAQ,UAAAF,cAAA,iBAAZA,cAAA,CAAcG,MAAM,IAAK,GAAG,CAAE,CACvCF,YAAY,CAAG,2CAA2C,CAC5D,CAAC,IAAM,IAAInD,GAAG,CAACsD,OAAO,CAAE,CACtBH,YAAY,CAAGnD,GAAG,CAACsD,OAAO,CAC5B,CAEA9D,QAAQ,CAAC2D,YAAY,CAAC,CAEtB;AACAvB,YAAY,CAACkB,UAAU,CAAC,uBAAuB,CAAC,CAClD,CAAC,OAAS,CACRxD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAiE,kBAAkB,CAAGA,CAAA,GAAM,CAC/B7D,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED;AACA,KAAM,CAAA8D,8BAA8B,CAAGA,CAAA,GAAM,CAC3C5D,0BAA0B,CAAC,KAAK,CAAC,CACnC,CAAC,CAED;AACA,KAAM,CAAA6D,qBAAqB,CAAGA,CAAA,GAAM,CAClCnF,WAAW,CAAC,CAAC,CAAC,CAChB,CAAC,CAED,mBACET,KAAA,CAACzB,GAAG,EAAAsH,QAAA,eACF/F,IAAA,CAACV,UAAU,EACT0G,KAAK,CAAC,wDAAgC,CACtCC,QAAQ,CAAC,mGAAsD,CAChE,CAAC,CAEDrE,KAAK,eAAI5B,IAAA,CAACR,UAAU,EAACmG,OAAO,CAAE/D,KAAM,CAAE,CAAC,cAExC1B,KAAA,CAACrB,KAAK,EAACqH,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACnB7F,KAAA,CAACpB,IAAI,EACHsH,KAAK,CAAE1F,QAAS,CAChB2F,QAAQ,CAAE9D,eAAgB,CAC1B+D,OAAO,CAAE/F,QAAQ,CAAG,WAAW,CAAG,UAAW,CAC7C2F,EAAE,CAAE,CAAEK,YAAY,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAU,CAAE,CAAAT,QAAA,eAEhD/F,IAAA,CAACjB,GAAG,EACF0H,IAAI,cAAEzG,IAAA,CAACZ,UAAU,GAAE,CAAE,CACrBsH,KAAK,CAAC,+BAAsB,CAC5BC,YAAY,CAAC,OAAO,CACrB,CAAC,CACD7F,gBAAgB,eACfd,IAAA,CAACjB,GAAG,EACF0H,IAAI,cAAEzG,IAAA,CAACX,WAAW,GAAE,CAAE,CACtBqH,KAAK,CAAC,gDAAyB,CAC/BC,YAAY,CAAC,OAAO,CACrB,CACF,EACG,CAAC,cAEPzG,KAAA,CAACzB,GAAG,EAACyH,EAAE,CAAE,CAAEU,CAAC,CAAE,CAAE,CAAE,CAAAb,QAAA,EACfrF,QAAQ,GAAK,CAAC,eACbV,IAAA,CAACP,YAAY,EACXmB,SAAS,CAAEA,SAAU,CACrBiG,gBAAgB,CAAE9D,oBAAqB,CACvC+D,QAAQ,CAAEpE,YAAa,CACvB1B,UAAU,CAAEA,UAAW,CACvBC,aAAa,CAAEA,aAAc,CAC7BS,OAAO,CAAEA,OAAQ,CAClB,CACF,CAEAhB,QAAQ,GAAK,CAAC,EAAII,gBAAgB,eACjCZ,KAAA,CAAAE,SAAA,EAAA2F,QAAA,eACE7F,KAAA,CAACzB,GAAG,EAACyH,EAAE,CAAE,CAAEa,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEd,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACzF7F,KAAA,CAACxB,UAAU,EAAC4H,OAAO,CAAC,IAAI,CAAAP,QAAA,EAAC,oBACX,CAACjF,gBAAgB,CAACoG,QAAQ,CACrCpG,gBAAgB,CAACqG,WAAW,OAAA/C,MAAA,CAAStD,gBAAgB,CAACqG,WAAW,KAAG,EAC3D,CAAC,cACbnH,IAAA,CAACf,MAAM,EACLqH,OAAO,CAAC,UAAU,CAClBc,SAAS,cAAEpH,IAAA,CAACZ,UAAU,GAAE,CAAE,CAC1BiI,OAAO,CAAEvB,qBAAsB,CAAAC,QAAA,CAChC,4BAED,CAAQ,CAAC,EACN,CAAC,cACN/F,IAAA,CAAChB,OAAO,EAACkH,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAEzBzE,OAAO,cACN1B,IAAA,CAACT,cAAc,GAAE,CAAC,cAElBS,IAAA,CAACN,oBAAoB,EACnBwB,SAAS,CAAEA,SAAU,CACrBoG,cAAc,CAAElE,kBAAmB,CACpC,CACF,EACD,CACH,EACE,CAAC,EACD,CAAC,cAERpD,IAAA,CAACL,WAAW,EACV4H,IAAI,CAAE/F,eAAgB,CACtB6B,QAAQ,CAAEjC,gBAAiB,CAC3BoG,OAAO,CAAE9D,sBAAuB,CAChC+D,QAAQ,CAAE9D,mBAAoB,CAC9BrC,eAAe,CAAEA,eAAgB,CACjCI,OAAO,CAAEA,OAAQ,CAClB,CAAC,cAEF1B,IAAA,CAACJ,mBAAmB,EAClB2H,IAAI,CAAEvF,uBAAwB,CAC9B2D,OAAO,CAAC,8EAAyC,CACjD6B,OAAO,CAAE3B,8BAA+B,CACzC,CAAC,cAEF7F,IAAA,CAACpB,QAAQ,EACP2I,IAAI,CAAE,CAAC,CAACzF,cAAe,CACvB4F,gBAAgB,CAAE,IAAK,CACvBF,OAAO,CAAE5B,kBAAmB,CAC5B+B,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAA9B,QAAA,cAE3D/F,IAAA,CAACrB,KAAK,EAAC6I,OAAO,CAAE5B,kBAAmB,CAACkC,QAAQ,CAAC,SAAS,CAAC5B,EAAE,CAAE,CAAE6B,KAAK,CAAE,MAAO,CAAE,CAAAhC,QAAA,CAC1EjE,cAAc,CACV,CAAC,CACA,CAAC,EACR,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}